FROM ubuntu:20.04

COPY sources_bionic.list /etc/apt/sources.list

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update \
	&& apt-get install -y python3 python3-dev python3-pip gcc vim libprotobuf-dev zlib1g zlib1g-dev libsm6 \
	&& apt-get install -y libgl1 libglib2.0-0 android-tools-adb

RUN cd /usr/bin \
	&& ln -sfn idle3 idle \
	&& ln -sfn pydoc3 pydoc \
	&& ln -sfn python3 python \
	&& ln -sfn python3-config python-config \
	&& ln -sfn pip3 pip \
	&& ls -al

RUN python -m pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host=pypi.tuna.tsinghua.edu.cn
RUN pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip3 config set install.trusted-host pypi.tuna.tsinghua.edu.cn

RUN python3 --version
RUN pip3 --version
COPY rknn_toolkit2-2.3.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl rknn_toolkit2-2.3.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
RUN pip3 install torch==1.10.1
RUN pip3 install rknn_toolkit2-2.3.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
RUN pip3 cache purge
