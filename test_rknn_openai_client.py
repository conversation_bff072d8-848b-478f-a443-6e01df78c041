#!/usr/bin/env python3
"""
RKNN OpenAI兼容API客户端测试脚本
"""

import requests
import json
import time
import argparse
from typing import Dict, Any

class RKNNOpenAIClient:
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化RKNN OpenAI客户端
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'RKNN-OpenAI-Client/1.0'
        })
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e), "status": "unhealthy"}
    
    def list_models(self) -> Dict[str, Any]:
        """列出可用模型"""
        try:
            response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def create_completion(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """创建文本补全"""
        data = {
            "prompt": prompt,
            "max_tokens": kwargs.get("max_tokens", 100),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 1.0),
            "n": kwargs.get("n", 1),
            "stream": kwargs.get("stream", False),
            "stop": kwargs.get("stop", None)
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/completions",
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def create_chat_completion(self, messages: list, **kwargs) -> Dict[str, Any]:
        """创建聊天补全"""
        data = {
            "messages": messages,
            "model": kwargs.get("model", "rknn-model"),
            "max_tokens": kwargs.get("max_tokens", 100),
            "temperature": kwargs.get("temperature", 0.7),
            "top_p": kwargs.get("top_p", 1.0),
            "n": kwargs.get("n", 1),
            "stream": kwargs.get("stream", False),
            "stop": kwargs.get("stop", None)
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=data,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}

def print_json(data: Dict[str, Any], title: str = ""):
    """格式化打印JSON数据"""
    if title:
        print(f"\n=== {title} ===")
    print(json.dumps(data, indent=2, ensure_ascii=False))

def test_health_check(client: RKNNOpenAIClient):
    """测试健康检查"""
    print("🔍 测试健康检查...")
    result = client.health_check()
    print_json(result, "健康检查结果")
    
    if "error" in result:
        print("❌ 健康检查失败")
        return False
    else:
        print("✅ 健康检查通过")
        return True

def test_list_models(client: RKNNOpenAIClient):
    """测试模型列表"""
    print("\n📋 测试模型列表...")
    result = client.list_models()
    print_json(result, "模型列表")
    
    if "error" in result:
        print("❌ 获取模型列表失败")
        return False
    else:
        print("✅ 获取模型列表成功")
        return True

def test_completion(client: RKNNOpenAIClient):
    """测试文本补全"""
    print("\n📝 测试文本补全...")
    
    prompt = "你好，请介绍一下RKNN"
    print(f"输入提示: {prompt}")
    
    start_time = time.time()
    result = client.create_completion(
        prompt=prompt,
        max_tokens=150,
        temperature=0.7
    )
    end_time = time.time()
    
    print_json(result, "文本补全结果")
    print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
    
    if "error" in result:
        print("❌ 文本补全失败")
        return False
    else:
        print("✅ 文本补全成功")
        return True

def test_chat_completion(client: RKNNOpenAIClient):
    """测试聊天补全"""
    print("\n💬 测试聊天补全...")
    
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手。"},
        {"role": "user", "content": "请解释什么是RKNN Toolkit2？"}
    ]
    
    print("输入消息:")
    for msg in messages:
        print(f"  {msg['role']}: {msg['content']}")
    
    start_time = time.time()
    result = client.create_chat_completion(
        messages=messages,
        max_tokens=200,
        temperature=0.7
    )
    end_time = time.time()
    
    print_json(result, "聊天补全结果")
    print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
    
    if "error" in result:
        print("❌ 聊天补全失败")
        return False
    else:
        print("✅ 聊天补全成功")
        return True

def interactive_chat(client: RKNNOpenAIClient):
    """交互式聊天"""
    print("\n🤖 进入交互式聊天模式")
    print("输入 'quit' 或 'exit' 退出")
    print("-" * 50)
    
    messages = [
        {"role": "system", "content": "你是一个基于RKNN的AI助手。"}
    ]
    
    while True:
        try:
            user_input = input("\n👤 用户: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            messages.append({"role": "user", "content": user_input})
            
            print("🤖 助手: 正在思考...")
            result = client.create_chat_completion(
                messages=messages,
                max_tokens=200,
                temperature=0.7
            )
            
            if "error" in result:
                print(f"❌ 错误: {result['error']}")
                continue
            
            assistant_message = result["choices"][0]["message"]["content"]
            print(f"🤖 助手: {assistant_message}")
            
            messages.append({"role": "assistant", "content": assistant_message})
            
            # 限制对话历史长度
            if len(messages) > 10:
                messages = messages[:1] + messages[-9:]
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RKNN OpenAI兼容API客户端测试')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='API服务器URL (默认: http://localhost:8000)')
    parser.add_argument('--test', choices=['all', 'health', 'models', 'completion', 'chat'],
                       default='all', help='要运行的测试 (默认: all)')
    parser.add_argument('--interactive', action='store_true',
                       help='启动交互式聊天模式')
    
    args = parser.parse_args()
    
    print("🚀 RKNN OpenAI兼容API客户端测试")
    print(f"🌐 服务器URL: {args.url}")
    print("=" * 50)
    
    client = RKNNOpenAIClient(args.url)
    
    # 运行测试
    test_results = []
    
    if args.test in ['all', 'health']:
        test_results.append(test_health_check(client))
    
    if args.test in ['all', 'models']:
        test_results.append(test_list_models(client))
    
    if args.test in ['all', 'completion']:
        test_results.append(test_completion(client))
    
    if args.test in ['all', 'chat']:
        test_results.append(test_chat_completion(client))
    
    # 显示测试结果摘要
    if test_results:
        print("\n" + "=" * 50)
        print("📊 测试结果摘要:")
        passed = sum(test_results)
        total = len(test_results)
        print(f"✅ 通过: {passed}/{total}")
        if passed == total:
            print("🎉 所有测试都通过了！")
        else:
            print("⚠️  部分测试失败，请检查服务器状态")
    
    # 交互式聊天
    if args.interactive:
        interactive_chat(client)

if __name__ == '__main__':
    main()
