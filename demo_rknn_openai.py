#!/usr/bin/env python3
"""
RKNN OpenAI服务演示脚本
展示如何使用RKNN模型提供OpenAI兼容的API服务
"""

import os
import sys
import time
import subprocess
import threading
import requests
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    RKNN OpenAI 兼容服务演示                    ║
║                                                              ║
║  基于 RKNN-Toolkit2 构建的 OpenAI 兼容 API 服务               ║
║  支持瑞芯微 NPU 硬件加速推理                                   ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    # 检查Python
    if sys.version_info < (3, 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['flask', 'numpy', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print(f"请运行: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查RKNN Toolkit2
    try:
        from rknn.api import RKNN
        print("✅ RKNN Toolkit2 已安装")
    except ImportError:
        print("❌ RKNN Toolkit2 未安装")
        print("请安装RKNN Toolkit2:")
        print("cd rknn-toolkit2/packages")
        print("pip install rknn_toolkit2-*.whl")
        return False
    
    print("✅ 环境检查通过")
    return True

def find_example_model():
    """查找示例模型"""
    print("🔍 查找示例模型...")
    
    # 可能的模型路径
    model_paths = [
        "rknn-toolkit2/examples/onnx/yolov5/yolov5s_relu.rknn",
        "rknn-toolkit2/examples/pytorch/resnet18/resnet18.rknn",
        "rknn-toolkit2/examples/tflite/mobilenet_v1/mobilenet_v1.rknn"
    ]
    
    for model_path in model_paths:
        if os.path.exists(model_path):
            print(f"✅ 找到模型: {model_path}")
            return model_path
    
    print("⚠️  未找到预构建的RKNN模型")
    print("您可以:")
    print("1. 运行示例脚本生成模型:")
    print("   cd rknn-toolkit2/examples/onnx/yolov5")
    print("   python test.py")
    print("2. 或者使用自己的RKNN模型文件")
    
    return None

def create_mock_model():
    """创建一个模拟的RKNN模型用于演示"""
    print("🔧 创建演示用的模拟模型...")
    
    mock_model_path = "demo_model.rknn"
    
    # 创建一个简单的模拟文件
    with open(mock_model_path, 'wb') as f:
        # 写入一些模拟数据
        f.write(b"MOCK_RKNN_MODEL_FOR_DEMO")
    
    print(f"✅ 创建模拟模型: {mock_model_path}")
    print("⚠️  注意: 这是一个模拟模型，仅用于演示API接口")
    
    return mock_model_path

def start_server_thread(model_path, port=8000):
    """在后台线程启动服务器"""
    def run_server():
        try:
            cmd = [
                sys.executable, "rknn_openai_server.py",
                "--model", model_path,
                "--host", "127.0.0.1",
                "--port", str(port)
            ]
            subprocess.run(cmd, check=True)
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    return server_thread

def wait_for_server(port=8000, timeout=30):
    """等待服务器启动"""
    print("⏳ 等待服务器启动...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"http://127.0.0.1:{port}/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器启动成功")
                return True
        except:
            pass
        time.sleep(1)
    
    print("❌ 服务器启动超时")
    return False

def demo_api_calls(port=8000):
    """演示API调用"""
    base_url = f"http://127.0.0.1:{port}"
    
    print("\n🚀 开始API演示...")
    print("=" * 60)
    
    # 1. 健康检查
    print("\n1️⃣  健康检查")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return
    
    # 2. 列出模型
    print("\n2️⃣  列出模型")
    try:
        response = requests.get(f"{base_url}/v1/models")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"❌ 列出模型失败: {e}")
    
    # 3. 文本补全
    print("\n3️⃣  文本补全")
    try:
        data = {
            "prompt": "RKNN是什么？请简单介绍一下。",
            "max_tokens": 100,
            "temperature": 0.7
        }
        response = requests.post(f"{base_url}/v1/completions", json=data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        if "choices" in result:
            print(f"生成的文本: {result['choices'][0]['text']}")
        else:
            print(f"响应: {result}")
    except Exception as e:
        print(f"❌ 文本补全失败: {e}")
    
    # 4. 聊天补全
    print("\n4️⃣  聊天补全")
    try:
        data = {
            "messages": [
                {"role": "system", "content": "你是一个有用的AI助手。"},
                {"role": "user", "content": "请介绍一下瑞芯微的NPU技术"}
            ],
            "max_tokens": 150,
            "temperature": 0.7
        }
        response = requests.post(f"{base_url}/v1/chat/completions", json=data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        if "choices" in result:
            print(f"助手回复: {result['choices'][0]['message']['content']}")
        else:
            print(f"响应: {result}")
    except Exception as e:
        print(f"❌ 聊天补全失败: {e}")
    
    print("\n✅ API演示完成")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_environment():
        return
    
    # 查找模型
    model_path = find_example_model()
    
    if not model_path:
        # 如果没有找到真实模型，创建一个模拟模型用于演示
        model_path = create_mock_model()
    
    print(f"\n📁 使用模型: {model_path}")
    
    # 启动服务器
    print("\n🚀 启动RKNN OpenAI兼容服务...")
    server_thread = start_server_thread(model_path)
    
    # 等待服务器启动
    if not wait_for_server():
        return
    
    # 演示API调用
    demo_api_calls()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("\n📚 更多信息:")
    print("- 查看 RKNN_OpenAI_Service_README.md 了解详细使用方法")
    print("- 运行 python test_rknn_openai_client.py --interactive 进入交互模式")
    print("- 访问 http://127.0.0.1:8000/health 查看服务状态")
    
    print("\n⚠️  注意: 服务器仍在后台运行，按 Ctrl+C 退出")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    main()
