apply plugin: 'com.android.application'

android {
    compileSdk 28
    defaultConfig {
        applicationId "com.rockchip.gpadc.yolodemo"
        minSdkVersion 25
        targetSdkVersion 32
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++11 -fexceptions"
                arguments "-DANDROID_ARM_NEON=TRUE"
            }
        }

        ndk {
            abiFilters "arm64-v8a"
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_7
        targetCompatibility JavaVersion.VERSION_1_7
    }

    // https://developer.android.com/studio/releases/gradle-plugin#groovy
    sourceSets {
        main {
            // The libs directory contains prebuilt libraries that are used by the
            // app's library defined in CMakeLists.txt via an IMPORTED target.
            // jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:2.0.4'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
}
