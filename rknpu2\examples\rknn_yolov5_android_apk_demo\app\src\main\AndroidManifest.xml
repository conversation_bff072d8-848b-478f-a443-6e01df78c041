<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.rockchip.gpadc.demo"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-feature android:name="android.hardware.camera" />


    <application
        android:allowBackup="true"
        android:icon="@drawable/rockchip"
        android:label="@string/app_name"
        android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
        <!--<activity-->
        <!--android:name=".MainActivity"-->
        <!--android:screenOrientation="landscape">-->
        <!--</activity>-->
        <activity
            android:name="com.rockchip.gpadc.demo.MainActivity"
            android:screenOrientation="sensorLandscape"
            android:label="@string/title_activity_start"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.rockchip.gpadc.demo.CameraPreviewActivity"
            android:screenOrientation="sensorLandscape"
            android:label="@string/title_activity_start"
            android:exported="false">
        </activity>
    </application>

</manifest>
