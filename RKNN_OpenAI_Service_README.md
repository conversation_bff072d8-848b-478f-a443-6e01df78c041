# RKNN OpenAI兼容服务

基于RKNN-Toolkit2构建的OpenAI兼容API服务，让您可以使用OpenAI的API格式来调用RKNN模型。

## 🚀 快速开始

### 1. 环境准备

确保您已经安装了RKNN-Toolkit2：

```bash
# 安装RKNN-Toolkit2
cd rknn-toolkit2/packages
pip install rknn_toolkit2-2.3.2-cp38-cp38-linux_x86_64.whl

# 安装其他依赖
pip install flask numpy requests
```

### 2. 准备RKNN模型

您需要一个已经转换好的RKNN模型文件（.rknn格式）。如果没有，可以使用项目中的示例：

```bash
# 使用YOLOv5示例模型
cd rknn-toolkit2/examples/onnx/yolov5
python test.py  # 这会生成 yolov5s_relu.rknn 文件
```

### 3. 启动服务

使用启动脚本：

```bash
# 给启动脚本执行权限
chmod +x start_rknn_openai_service.sh

# 启动服务（替换为您的模型路径）
./start_rknn_openai_service.sh -m ./rknn-toolkit2/examples/onnx/yolov5/yolov5s_relu.rknn
```

或者直接使用Python：

```bash
python3 rknn_openai_server.py --model ./path/to/your/model.rknn --host 0.0.0.0 --port 8000
```

### 4. 测试服务

```bash
# 运行测试脚本
python3 test_rknn_openai_client.py

# 或者进入交互式聊天模式
python3 test_rknn_openai_client.py --interactive
```

## 📋 API端点

服务提供以下OpenAI兼容的API端点：

### 健康检查
```http
GET /health
```

### 列出模型
```http
GET /v1/models
```

### 文本补全
```http
POST /v1/completions
Content-Type: application/json

{
  "prompt": "你好，请介绍一下RKNN",
  "max_tokens": 100,
  "temperature": 0.7
}
```

### 聊天补全
```http
POST /v1/chat/completions
Content-Type: application/json

{
  "messages": [
    {"role": "system", "content": "你是一个有用的AI助手。"},
    {"role": "user", "content": "什么是RKNN？"}
  ],
  "max_tokens": 150,
  "temperature": 0.7
}
```

## 🛠️ 配置选项

### 启动参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--model` | RKNN模型文件路径 | 必需 |
| `--host` | 服务器主机地址 | `0.0.0.0` |
| `--port` | 服务器端口 | `8000` |
| `--platform` | 目标平台 | `rk3588` |

### 支持的平台

- `rk3588` (默认)
- `rk3576`
- `rk3566`
- `rk3568`
- `rk3562`
- `rv1103`
- `rv1106`
- `rk2118`

## 🔧 在瑞芯微硬件上运行

如果您在瑞芯微硬件上运行，需要先启动`rknn_server`：

### Android平台
```bash
# 推送rknn_server到设备
adb push runtime/Android/rknn_server/arm64/rknn_server /vendor/bin/
adb shell chmod +x /vendor/bin/rknn_server

# 启动服务
adb shell "nohup /vendor/bin/rknn_server >/dev/null"&
```

### Linux平台
```bash
# 推送rknn_server到设备
adb push runtime/Linux/rknn_server/aarch64/usr/bin/rknn_server /usr/bin/
adb shell chmod +x /usr/bin/rknn_server

# 启动服务
adb shell "nohup /usr/bin/rknn_server >/dev/null"&
```

详细说明请参考：`doc/rknn_server_proxy.md`

## 📝 使用示例

### Python客户端示例

```python
import requests

# 健康检查
response = requests.get("http://localhost:8000/health")
print(response.json())

# 文本补全
completion_data = {
    "prompt": "RKNN是什么？",
    "max_tokens": 100,
    "temperature": 0.7
}
response = requests.post("http://localhost:8000/v1/completions", json=completion_data)
print(response.json())

# 聊天补全
chat_data = {
    "messages": [
        {"role": "user", "content": "介绍一下瑞芯微的NPU"}
    ],
    "max_tokens": 150
}
response = requests.post("http://localhost:8000/v1/chat/completions", json=chat_data)
print(response.json())
```

### curl示例

```bash
# 健康检查
curl http://localhost:8000/health

# 列出模型
curl http://localhost:8000/v1/models

# 文本补全
curl -X POST http://localhost:8000/v1/completions \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "RKNN是什么？",
    "max_tokens": 100,
    "temperature": 0.7
  }'

# 聊天补全
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "介绍一下瑞芯微的NPU"}
    ],
    "max_tokens": 150
  }'
```

## 🔍 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确保模型文件是有效的RKNN格式
   - 检查目标平台是否匹配

2. **连接失败**
   - 确保服务器正在运行
   - 检查端口是否被占用
   - 验证防火墙设置

3. **在瑞芯微硬件上运行失败**
   - 确保`rknn_server`正在运行
   - 检查Runtime库版本是否匹配
   - 参考`doc/rknn_server_proxy.md`

### 调试模式

启用详细日志：

```bash
# 设置环境变量
export RKNN_SERVER_LOGLEVEL=5

# 启动服务
python3 rknn_openai_server.py --model ./model.rknn
```

## 📚 更多信息

- [RKNN-Toolkit2 官方文档](./doc/)
- [RKNN服务器代理文档](./doc/rknn_server_proxy.md)
- [RKNN模型动物园](https://github.com/airockchip/rknn_model_zoo)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目遵循与RKNN-Toolkit2相同的许可证。
