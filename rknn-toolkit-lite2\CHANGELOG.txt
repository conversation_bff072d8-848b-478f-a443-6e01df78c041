2025-04-03
版本: v2.3.2
1. 支持 RV1126B。

2024-11-06
版本: v2.3.0
1. whl包标准化(manylinux)

2024-09-14
版本: v2.2.0
1. 修复已知BUG。

2024-08-08
版本: v2.1.0
1. 修复已知BUG。

2024-03-14
版本: v2.0.0b0
1. 功能完善：
   1.1 新增对RK3576的支持；
   1.2 init_runtime core_mask参数增加新的可选值：RKNNLite.NPU_CORE_ALL。

2023-12-04
版本: v1.6.0
1. 功能完善：
   1.1 新增适配AARCH64 Python3.11的安装包；
   1.2 推理接口增加对动态shape模型的支持；
   1.3 推理接口增加对4维NCHW格式输入的支持。

2023-08-21
版本: v1.5.2
1. 更新版本号

版本: v1.5.0
1. 功能完善：
   1.1 新增对RK3562的支持；
   1.2 新增适配AARCH64 Python3.8, Python3.10的安装包；
   1.3 适配1.5.0版本NPU驱动。

2022-08-31
版本: v1.4.0
1. 功能完善：
   1.1 init_runtime core_mask支持NPU_CORE_0_1和NPU_CORE_0_1_2；
   1.2 适配1.4.0版本NPU驱动。

2022-04-27
版本: v1.3.0
1. 功能完善：
   1.1 完善init_runtime失败的提示信息；
   1.2 适配1.3.0版本NPU驱动。

2022-01-14
版本：v1.2.0
1. 新功能：
   1.1 RKNN模型推理；
   1.2 SDK版本查询；
   1.3 模型可运行平台查询。
