#!/bin/bash

# RKNN OpenAI兼容服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_MODEL_PATH=""
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8000"
DEFAULT_PLATFORM="rk3588"

# 显示帮助信息
show_help() {
    cat << EOF
RKNN OpenAI兼容服务启动脚本

用法: $0 [选项]

选项:
    -m, --model PATH        RKNN模型文件路径 (必需)
    -h, --host HOST         服务器主机地址 (默认: $DEFAULT_HOST)
    -p, --port PORT         服务器端口 (默认: $DEFAULT_PORT)
    --platform PLATFORM    目标平台 (默认: $DEFAULT_PLATFORM)
    --help                  显示此帮助信息

支持的平台:
    - rk3588 (默认)
    - rk3576
    - rk3566
    - rk3568
    - rk3562
    - rv1103
    - rv1106
    - rk2118

示例:
    # 使用YOLOv5模型启动服务
    $0 -m ./rknn-toolkit2/examples/onnx/yolov5/yolov5s_relu.rknn

    # 指定端口和平台
    $0 -m ./model.rknn -p 8080 --platform rk3576

    # 绑定到特定IP
    $0 -m ./model.rknn -h ************* -p 8000

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--model)
                MODEL_PATH="$2"
                shift 2
                ;;
            -h|--host)
                HOST="$2"
                shift 2
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            --platform)
                PLATFORM="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置默认值
    MODEL_PATH=${MODEL_PATH:-$DEFAULT_MODEL_PATH}
    HOST=${HOST:-$DEFAULT_HOST}
    PORT=${PORT:-$DEFAULT_PORT}
    PLATFORM=${PLATFORM:-$DEFAULT_PLATFORM}
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."

    # 检查Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi

    # 检查pip包
    if ! python3 -c "import flask" &> /dev/null; then
        print_warning "Flask 未安装，正在安装..."
        pip3 install flask
    fi

    if ! python3 -c "import numpy" &> /dev/null; then
        print_warning "NumPy 未安装，正在安装..."
        pip3 install numpy
    fi

    # 检查RKNN Toolkit2
    if ! python3 -c "from rknn.api import RKNN" &> /dev/null; then
        print_error "RKNN Toolkit2 未安装"
        print_info "请安装RKNN Toolkit2:"
        print_info "cd rknn-toolkit2/packages"
        print_info "pip3 install rknn_toolkit2-*.whl"
        exit 1
    fi

    print_success "依赖检查完成"
}

# 验证参数
validate_args() {
    print_info "验证参数..."

    # 检查模型文件
    if [[ -z "$MODEL_PATH" ]]; then
        print_error "必须指定模型文件路径"
        show_help
        exit 1
    fi

    if [[ ! -f "$MODEL_PATH" ]]; then
        print_error "模型文件不存在: $MODEL_PATH"
        exit 1
    fi

    # 检查端口
    if ! [[ "$PORT" =~ ^[0-9]+$ ]] || [[ "$PORT" -lt 1 ]] || [[ "$PORT" -gt 65535 ]]; then
        print_error "无效的端口号: $PORT"
        exit 1
    fi

    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":$PORT "; then
        print_warning "端口 $PORT 可能已被占用"
    fi

    print_success "参数验证完成"
}

# 检查RKNN服务器状态
check_rknn_server() {
    print_info "检查RKNN服务器状态..."
    
    # 如果在瑞芯微硬件上运行，检查rknn_server进程
    if [[ -f "/proc/device-tree/compatible" ]]; then
        if grep -q "rockchip" /proc/device-tree/compatible 2>/dev/null; then
            if ! pgrep -f "rknn_server" > /dev/null; then
                print_warning "rknn_server 未运行"
                print_info "如果在瑞芯微硬件上运行，请先启动 rknn_server"
                print_info "参考文档: doc/rknn_server_proxy.md"
            else
                print_success "rknn_server 正在运行"
            fi
        fi
    fi
}

# 启动服务
start_service() {
    print_info "启动RKNN OpenAI兼容服务..."
    print_info "模型文件: $MODEL_PATH"
    print_info "服务地址: http://$HOST:$PORT"
    print_info "目标平台: $PLATFORM"
    print_info ""
    print_info "API端点:"
    print_info "  - GET  /v1/models"
    print_info "  - POST /v1/completions"
    print_info "  - POST /v1/chat/completions"
    print_info "  - GET  /health"
    print_info ""
    print_info "按 Ctrl+C 停止服务"
    print_info ""

    # 启动Python服务器
    python3 rknn_openai_server.py \
        --model "$MODEL_PATH" \
        --host "$HOST" \
        --port "$PORT" \
        --platform "$PLATFORM"
}

# 主函数
main() {
    print_info "RKNN OpenAI兼容服务启动器"
    print_info "================================"

    parse_args "$@"
    check_dependencies
    validate_args
    check_rknn_server
    start_service
}

# 信号处理
trap 'print_info "正在停止服务..."; exit 0' INT TERM

# 运行主函数
main "$@"
