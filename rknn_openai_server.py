#!/usr/bin/env python3
"""
RKNN OpenAI兼容API服务器
基于RKNN-Toolkit2构建的OpenAI兼容API服务
"""

import os
import json
import time
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime

from flask import Flask, request, jsonify, Response
import numpy as np
from rknn.api import RKNN

app = Flask(__name__)

class RKNNOpenAIServer:
    def __init__(self, model_path: str, target_platform: str = 'rk3588'):
        """
        初始化RKNN OpenAI服务器
        
        Args:
            model_path: RKNN模型文件路径
            target_platform: 目标平台
        """
        self.model_path = model_path
        self.target_platform = target_platform
        self.rknn = None
        self.model_info = {
            "id": "rknn-model",
            "object": "model",
            "created": int(time.time()),
            "owned_by": "rknn-toolkit2"
        }
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化RKNN模型"""
        try:
            self.rknn = RKNN(verbose=False)
            
            # 加载RKNN模型
            print(f"正在加载RKNN模型: {self.model_path}")
            ret = self.rknn.load_rknn(self.model_path)
            if ret != 0:
                raise Exception(f"加载RKNN模型失败: {ret}")
            
            # 初始化运行时环境
            print("正在初始化运行时环境...")
            ret = self.rknn.init_runtime(target=self.target_platform)
            if ret != 0:
                raise Exception(f"初始化运行时环境失败: {ret}")
            
            print("RKNN模型初始化成功!")
            
        except Exception as e:
            print(f"RKNN模型初始化失败: {e}")
            raise
    
    def inference(self, input_data: np.ndarray) -> np.ndarray:
        """执行推理"""
        if self.rknn is None:
            raise Exception("RKNN模型未初始化")
        
        try:
            outputs = self.rknn.inference(inputs=[input_data])
            return outputs[0] if outputs else None
        except Exception as e:
            print(f"推理失败: {e}")
            raise
    
    def cleanup(self):
        """清理资源"""
        if self.rknn:
            self.rknn.release()

# 全局RKNN服务器实例
rknn_server = None

@app.route('/v1/models', methods=['GET'])
def list_models():
    """列出可用模型"""
    return jsonify({
        "object": "list",
        "data": [rknn_server.model_info] if rknn_server else []
    })

@app.route('/v1/models/<model_id>', methods=['GET'])
def get_model(model_id: str):
    """获取特定模型信息"""
    if not rknn_server or model_id != rknn_server.model_info["id"]:
        return jsonify({"error": "Model not found"}), 404
    
    return jsonify(rknn_server.model_info)

@app.route('/v1/completions', methods=['POST'])
def create_completion():
    """创建文本补全（适配RKNN推理）"""
    try:
        data = request.get_json()
        
        # 基本参数验证
        if not data or 'prompt' not in data:
            return jsonify({"error": "Missing prompt"}), 400
        
        prompt = data['prompt']
        max_tokens = data.get('max_tokens', 100)
        temperature = data.get('temperature', 0.7)
        
        # 这里需要根据您的具体模型来处理输入
        # 示例：将文本转换为模型输入格式
        # input_data = preprocess_text(prompt)
        
        # 模拟推理结果（您需要根据实际模型调整）
        response_text = f"基于RKNN模型的响应: {prompt}"
        
        # 构造OpenAI兼容的响应
        response = {
            "id": f"cmpl-{uuid.uuid4().hex}",
            "object": "text_completion",
            "created": int(time.time()),
            "model": rknn_server.model_info["id"],
            "choices": [{
                "text": response_text,
                "index": 0,
                "logprobs": None,
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(prompt.split()) + len(response_text.split())
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/chat/completions', methods=['POST'])
def create_chat_completion():
    """创建聊天补全"""
    try:
        data = request.get_json()
        
        if not data or 'messages' not in data:
            return jsonify({"error": "Missing messages"}), 400
        
        messages = data['messages']
        model = data.get('model', rknn_server.model_info["id"])
        
        # 提取最后一条用户消息
        user_message = ""
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break
        
        # 这里需要根据您的具体模型来处理聊天输入
        # 示例响应
        assistant_response = f"RKNN模型回复: {user_message}"
        
        response = {
            "id": f"chatcmpl-{uuid.uuid4().hex}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": assistant_response
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(assistant_response.split()),
                "total_tokens": len(user_message.split()) + len(assistant_response.split())
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        "status": "healthy",
        "model_loaded": rknn_server is not None,
        "timestamp": datetime.now().isoformat()
    })

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='RKNN OpenAI兼容API服务器')
    parser.add_argument('--model', required=True, help='RKNN模型文件路径')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--platform', default='rk3588', help='目标平台')
    
    args = parser.parse_args()
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        return
    
    # 初始化全局RKNN服务器
    global rknn_server
    try:
        rknn_server = RKNNOpenAIServer(args.model, args.platform)
        print(f"RKNN OpenAI兼容服务器启动成功!")
        print(f"模型: {args.model}")
        print(f"平台: {args.platform}")
        print(f"服务地址: http://{args.host}:{args.port}")
        print(f"API文档: http://{args.host}:{args.port}/health")
        
        # 启动Flask服务器
        app.run(host=args.host, port=args.port, debug=False)
        
    except Exception as e:
        print(f"启动失败: {e}")
    finally:
        if rknn_server:
            rknn_server.cleanup()

if __name__ == '__main__':
    main()
