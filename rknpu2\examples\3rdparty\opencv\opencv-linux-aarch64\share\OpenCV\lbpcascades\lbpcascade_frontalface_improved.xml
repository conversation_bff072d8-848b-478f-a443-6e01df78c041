<?xml version="1.0"?>
<!--
//////////////////////////////////////////////////////////////////////////
| Contributors License Agreement
| IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
|   By downloading, copying, installing or using the software you agree
|   to this license. If you do not agree to this license, do not download,
|   install, copy or use the software.
|
| Copyright (c) 2017, <PERSON><PERSON><PERSON>, Can Ergun and Toon Goedeme
| (KU Leuven, EAVISE Research Group, Jan <PERSON> 5,
| Sint-Kateli<PERSON>e-<PERSON>r, Belgium).
| All rights reserved.
|
| Redistribution and use in source and binary forms, with or without
| modification, are permitted provided that the following conditions are
| met:
|
|    * Redistributions of source code must retain the above copyright
|       notice, this list of conditions and the following disclaimer.
|    * Redistributions in binary form must reproduce the above
|      copyright notice, this list of conditions and the following
|      disclaimer in the documentation and/or other materials provided
|      with the distribution.
|    * The name of Contributor may not used to endorse or promote products
|      derived from this software without specific prior written permission.
|
| THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
| "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
| LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
| A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
| CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
| EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
| PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
| PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
| LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
| NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
| SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//////////////////////////////////////////////////////////////////////////

=====================================================================
Improving Open Source Face Detection by Combining an Adapted Cascade
Classification Pipeline and Active Learning
=====================================================================
by Puttemans Steven, Can Ergun and Toon Goedeme (KU Leuven, EAVISE, Belgium)

This model is the best performing IterativeHardPositives+ frontal face detection model
used in the research paper presented at VISAPP2017, Porto, Portugal.

RESEARCHERS:
If you are using the improved face detection model or involved ideas please cite
this paper (available at http://eavise.be/publications_lirias.php):

@InProceedings{PuttemansVISAPP2017,
  author =       "Puttemans Steven, Can Ergun and Toon Goedeme",
  title =        "Improving Open Source Face Detection by Combining an Adapted Cascade Classification Pipeline and Active Learning"
  booktitle =    "12th International Conference on Computer Vision Theory and Applications"
  year =         "2017",
  month =        "February"
}

COMMERCIAL:
If you have any commercial interest in this work please contact
steven.puttemans@kuleuven.<NAME_EMAIL>
-->

<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>LBP</featureType>
  <height>45</height>
  <width>45</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9500000476837158e-001</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-001</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-001</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>256</maxCatCount>
    <featSize>1</featSize></featureParams>
  <stageNum>19</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>6</maxWeakCount>
      <stageThreshold>-4.1617846488952637e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 26 -1 -1 -17409 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.9726462364196777e-001 -3.8938775658607483e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 -1 -1 -21569 -20545 -1 -1 -20545 -1</internalNodes>
          <leafValues>
            -9.8648911714553833e-001 -2.5386649370193481e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -21569 -16449 1006578219 -20801 -16449 -1 -21585 -1</internalNodes>
          <leafValues>
            -9.6436238288879395e-001 -1.4039695262908936e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 -1 -1 -16402 -4370 -1 -1 -1053010 -4456466</internalNodes>
          <leafValues>
            -8.4081345796585083e-001 3.8321062922477722e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -184747280 -705314819 1326353 1364574079 -131073 -5
            2147481147 -1</internalNodes>
          <leafValues>
            -8.1084597110748291e-001 4.3495711684226990e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 -142618625 -4097 -37269 -20933 872350430 -268476417
            1207894255 2139032115</internalNodes>
          <leafValues>
            -7.3140043020248413e-001 4.3799084424972534e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>6</maxWeakCount>
      <stageThreshold>-4.0652265548706055e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 19 -1 -1 -17409 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.9727255105972290e-001 -7.2050148248672485e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -1 1073741823 -1 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.8717331886291504e-001 -5.3031939268112183e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -16385 -1 -21569 -20545 -1 -1 -21569 -1</internalNodes>
          <leafValues>
            -9.3442338705062866e-001 6.5213099122047424e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -2097153 -1 -1 -1 -1 -8193 -1 -35467</internalNodes>
          <leafValues>
            -7.9567342996597290e-001 4.2883640527725220e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 -134239573 -16465 58663467 -1079022929 -1073758273
            -81937 -8412501 -404766817</internalNodes>
          <leafValues>
            -7.1264797449111938e-001 4.1050794720649719e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 -17047555 -1099008003 2147479551 -1090584581 -69633
            -1342177281 -1090650121 -1472692240</internalNodes>
          <leafValues>
            -7.6119172573089600e-001 4.2042696475982666e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>7</maxWeakCount>
      <stageThreshold>-4.6904473304748535e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 12 -1 -1 -17409 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.9725550413131714e-001 -8.3142280578613281e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -1 -168429569 -1 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.8183268308639526e-001 -3.6373397707939148e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -1 1073741759 -1 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.1890293359756470e-001 7.8322596848011017e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 -17409 -2097153 -134372726 -21873 -65 -536870913
            -161109 -4215889</internalNodes>
          <leafValues>
            -8.0752444267272949e-001 1.9565649330615997e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 -469779457 -286371842 -33619971 -212993 -1 -41943049
            -134217731 -1346863620</internalNodes>
          <leafValues>
            -6.9232726097106934e-001 3.8141927123069763e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 -1896950780 -1964839052 -9 707723004 -34078727
            -1074266122 -536872969 -262145</internalNodes>
          <leafValues>
            -8.1760478019714355e-001 3.4172961115837097e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -402657501 654311423 -419533278 -452984853
            1979676215 -1208090625 -167772569 -524289</internalNodes>
          <leafValues>
            -6.3433408737182617e-001 4.3154156208038330e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-4.2590322494506836e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 42 -1 -655361 -1 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.9715477228164673e-001 -8.6178696155548096e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 -1 -705300491 -1 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.8356908559799194e-001 -5.7423096895217896e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 -65 872413111 -2049 -1 -1 -1 -1 -1</internalNodes>
          <leafValues>
            -9.2525935173034668e-001 -1.3835857808589935e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 -1 -5242881 -1 -524289 -4194305 -1 -1 -43148</internalNodes>
          <leafValues>
            -7.8076487779617310e-001 1.8362471461296082e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -145227841 868203194 -1627394049 935050171
            2147483647 1006600191 -268439637 1002437615</internalNodes>
          <leafValues>
            -7.2554033994674683e-001 3.3393219113349915e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 -214961408 50592514 -2128 1072162674 -1077940293
            -1084489966 -134219854 -1074790401</internalNodes>
          <leafValues>
            -6.1547595262527466e-001 3.9214438199996948e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 -294987948 -1124421633 -73729 -268435841 -33654928
            2122317823 -268599297 -33554945</internalNodes>
          <leafValues>
            -6.4863425493240356e-001 3.8784855604171753e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -525585 -26738821 -17895690 1123482236 1996455758
            -8519849 -252182980 -461898753</internalNodes>
          <leafValues>
            -5.5464369058609009e-001 4.4275921583175659e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-4.0009465217590332e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 82 -1 -1 -1 -1 -33685505 -1 -1 -1</internalNodes>
          <leafValues>
            -9.9707120656967163e-001 -8.9196771383285522e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -1 -1 -1 -1 2147446783 -1 -1 -1</internalNodes>
          <leafValues>
            -9.8670446872711182e-001 -7.5064390897750854e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 -1 -1 -262145 -1 -252379137 -1 -1 -1</internalNodes>
          <leafValues>
            -8.9446705579757690e-001 7.0268943905830383e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -1 -8201 -1 -2097153 -16777217 -513 -16777217
            -1162149889</internalNodes>
          <leafValues>
            -7.2166109085083008e-001 2.9786801338195801e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -21569 -1069121 1006578211 -134238545 -16450
            -268599297 -21617 -14680097</internalNodes>
          <leafValues>
            -6.2449234724044800e-001 3.8551881909370422e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 -268701913 -1999962377 1995165474 -453316822
            1744684853 -2063597697 -134226057 -50336769</internalNodes>
          <leafValues>
            -5.5207914113998413e-001 4.2211884260177612e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 -352321825 -526489 -420020626 -486605074 1155483470
            -110104705 -587840772 -25428801</internalNodes>
          <leafValues>
            -5.3324747085571289e-001 4.4535955786705017e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 70270772 2012790229 -16810020 -245764 -1208090635
            -753667 -1073741828 -1363662420</internalNodes>
          <leafValues>
            -6.4402890205383301e-001 3.8995954394340515e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-4.6897511482238770e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 97 -1 -1 -1 -1 -524289 -524289 -1 -1</internalNodes>
          <leafValues>
            -9.9684870243072510e-001 -8.8232177495956421e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -1 -1 -1 -1 2147438591 -1 -1 -1</internalNodes>
          <leafValues>
            -9.8677414655685425e-001 -7.8965580463409424e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 -1 -1 -1 -1 -1048577 -262149 -1048577 -35339</internalNodes>
          <leafValues>
            -9.2621946334838867e-001 -2.9984828829765320e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -2249 867434291 -32769 -33562753 -1 -1073758209
            -4165 -1</internalNodes>
          <leafValues>
            -7.2429555654525757e-001 2.2348840534687042e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 1659068671 -142606337 587132538 -67108993 577718271
            -294921 -134479873 -129</internalNodes>
          <leafValues>
            -5.5495566129684448e-001 3.5419258475303650e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -268441813 788267007 -286265494 -486576145 -8920251
            2138505075 -151652570 -2050</internalNodes>
          <leafValues>
            -5.3362584114074707e-001 3.9479774236679077e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 -1368387212 -537102978 -98305 -163843 1065109500
            -16777217 -67321939 -1141359619</internalNodes>
          <leafValues>
            -5.6162708997726440e-001 3.8008108735084534e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -268435550 1781120906 -251658720 -143130698
            -1048605 -1887436825 1979700688 -1008730125</internalNodes>
          <leafValues>
            -5.1167154312133789e-001 4.0678605437278748e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-4.2179841995239258e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 97 -1 -1 -1 -1 -524289 -524289 -1 -1</internalNodes>
          <leafValues>
            -9.9685418605804443e-001 -8.8037383556365967e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 -1 -1 -1 -1 -8912897 -524297 -8912897 -1</internalNodes>
          <leafValues>
            -9.7972750663757324e-001 -5.7626229524612427e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -1 -1 -1 -1 -1 -65 -1 -2249</internalNodes>
          <leafValues>
            -9.0239793062210083e-001 -1.7454113066196442e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -1 -4097 -1 -513 -16777217 -268468483 -16797697
            -1430589697</internalNodes>
          <leafValues>
            -7.4346423149108887e-001 9.4165161252021790e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 1364588304 -581845274 -536936460 -3 -308936705
            -1074331649 -4196865 -134225953</internalNodes>
          <leafValues>
            -6.8877440690994263e-001 2.7647304534912109e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 -37765187 -540675 -3 -327753 -1082458115 -65537
            1071611901 536827253</internalNodes>
          <leafValues>
            -5.7555085420608521e-001 3.4339720010757446e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 -269490650 -1561395522 -1343312090 -857083986
            -1073750223 -369098755 -50856110 -2065</internalNodes>
          <leafValues>
            -5.4036927223205566e-001 4.0065473318099976e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 -425668880 -34427164 1879048177 -269570140 790740912
            -196740 2138535839 -536918145</internalNodes>
          <leafValues>
            -4.8439365625381470e-001 4.4630467891693115e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 74726960 -1246482434 -1 -246017 -1078607916
            -1073947163 -1644231687 -1359211496</internalNodes>
          <leafValues>
            -5.6686979532241821e-001 3.6671569943428040e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -135274809 -1158173459 -353176850 540195262
            2139086600 2071977814 -546898600 -96272673</internalNodes>
          <leafValues>
            -5.1499199867248535e-001 4.0788397192955017e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-4.0345416069030762e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 78 -1 -1 -1 -1 -8912897 -1 -8912897 -1</internalNodes>
          <leafValues>
            -9.9573624134063721e-001 -8.5452395677566528e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 -1 -1 -1 -1 -148635649 -524297 -8912897 -1</internalNodes>
          <leafValues>
            -9.7307401895523071e-001 -5.2884924411773682e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -1 -8209 -1 -257 -772734977 -1 -201850881 -1</internalNodes>
          <leafValues>
            -8.6225658655166626e-001 4.3712578713893890e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -570427393 -16649 -69633 -131073 -536944677 -1 -8737
            -1435828225</internalNodes>
          <leafValues>
            -6.8078064918518066e-001 2.5120577216148376e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -1179697 -34082849 -3278356 -37429266 -1048578
            -555753474 -1015551096 -37489685</internalNodes>
          <leafValues>
            -6.1699724197387695e-001 3.0963841080665588e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -1931606992 -17548804 -16842753 -1075021827
            1073667572 -81921 -1611073620 -1415047752</internalNodes>
          <leafValues>
            -6.0499197244644165e-001 3.0735063552856445e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -269754813 1761591286 -1073811523 2130378623 -17580
            -1082294665 -159514800 -1026883840</internalNodes>
          <leafValues>
            -5.6772041320800781e-001 3.5023149847984314e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 2016561683 1528827871 -10258447 960184191 125476830
            -8511618 -1078239365 187648611</internalNodes>
          <leafValues>
            -5.5894804000854492e-001 3.4856522083282471e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -207423502 -333902 2013200231 -202348848 1042454451
            -16393 1073117139 2004162321</internalNodes>
          <leafValues>
            -5.7197356224060059e-001 3.2818377017974854e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-3.4892759323120117e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 78 -1 -1 -1 -1 -8912897 -1 -8912897 -1</internalNodes>
          <leafValues>
            -9.8917990922927856e-001 -7.3812037706375122e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 -1 -1 -1 -1 -148635649 -524297 -8912897 -1</internalNodes>
          <leafValues>
            -9.3414896726608276e-001 -2.6945295929908752e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 -1 -524289 -1 -1048577 1879011071 -32769 -524289
            -3178753</internalNodes>
          <leafValues>
            -7.6891708374023438e-001 5.2568886429071426e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 -352329729 -17891329 -16810117 -486871042 -688128841
            -1358954675 -16777218 -219217968</internalNodes>
          <leafValues>
            -6.2337344884872437e-001 2.5143685936927795e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -2157 -1548812374 -1343233440 -418381854 -953155613
            -836960513 -713571200 -709888014</internalNodes>
          <leafValues>
            -4.7277018427848816e-001 3.9616456627845764e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -1094717701 -67240065 -65857 -32899 -5783756
            -136446081 -134285352 -2003298884</internalNodes>
          <leafValues>
            -5.1766264438629150e-001 3.5814732313156128e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -218830160 -119671186 5505075 1241491391 -1594469
            -2097185 2004828075 -67649541</internalNodes>
          <leafValues>
            -6.5394639968872070e-001 3.0377501249313354e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 -551814749 2099511088 -1090732551 -2045546512
            -1086341441 1059848178 800042912 252705994</internalNodes>
          <leafValues>
            -5.2584588527679443e-001 3.3847147226333618e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 -272651477 578776766 -285233490 -889225217
            2147448656 377454463 2012701952 -68157761</internalNodes>
          <leafValues>
            -6.1836904287338257e-001 2.8922611474990845e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-3.0220029354095459e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 36 -1 -570425345 -1 -570425345 -1 -50331649 -6291457 -1</internalNodes>
          <leafValues>
            -9.7703826427459717e-001 -6.2527233362197876e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -1430602241 -33619969 -1 -3 -1074003969 -1073758209
            -1073741825 -1073768705</internalNodes>
          <leafValues>
            -8.9538317918777466e-001 -3.1887885928153992e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 -1 -268439625 -65601 -268439569 -393809 -270532609
            -42076889 -288361721</internalNodes>
          <leafValues>
            -6.8733429908752441e-001 1.2978810071945190e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 -755049252 2042563807 1795096575 465121071
            -1090585188 -20609 -1459691784 539672495</internalNodes>
          <leafValues>
            -5.7038843631744385e-001 3.0220884084701538e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 -94377762 -25702678 1694167798 -231224662 1079955016
            -346144140 2029995743 -536918961</internalNodes>
          <leafValues>
            -5.3204691410064697e-001 3.4054222702980042e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 2143026943 -285278225 -3 -612438281 -16403 -131074
            -1 -1430749256</internalNodes>
          <leafValues>
            -4.6176829934120178e-001 4.1114711761474609e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 203424336 -25378820 -35667973 1073360894 -1912815660
            -573444 -356583491 -1365235056</internalNodes>
          <leafValues>
            -4.9911966919898987e-001 3.5335537791252136e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 -1056773 -1508430 -558153 -102747408 2133997491
            -269043865 2004842231 -8947721</internalNodes>
          <leafValues>
            -4.0219521522521973e-001 4.3947893381118774e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 -880809694 -1070282769 -1363162108 -838881281
            -680395161 -2064124929 -34244753 1173880701</internalNodes>
          <leafValues>
            -5.3891533613204956e-001 3.2062566280364990e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-2.5489892959594727e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 39 -1 -572522497 -8519681 -570425345 -4195329 -50333249
            -1 -1</internalNodes>
          <leafValues>
            -9.4647216796875000e-001 -3.3662387728691101e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -1430735362 -33619971 -8201 -3 -1677983745
            -1073762817 -1074003969 -1142979329</internalNodes>
          <leafValues>
            -8.0300611257553101e-001 -3.8466516882181168e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 -67113217 -524289 -671482265 -786461 1677132031
            -268473345 -68005889 -70291765</internalNodes>
          <leafValues>
            -5.8367580175399780e-001 2.6507318019866943e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 -277872641 -553910292 -268435458 -16843010
            1542420439 -1342178311 -143132940 -2834</internalNodes>
          <leafValues>
            -4.6897178888320923e-001 3.7864661216735840e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 -1312789 -290527285 -286326862 -5505280 -1712335966
            -2045979188 1165423617 -709363723</internalNodes>
          <leafValues>
            -4.6382644772529602e-001 3.6114525794982910e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 1355856590 -109445156 -96665606 2066939898
            1356084692 1549031917 -30146561 -16581701</internalNodes>
          <leafValues>
            -6.3095021247863770e-001 2.9294869303703308e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -335555328 118529 1860167712 -810680357 -33558656
            -1368391795 -402663552 -1343225921</internalNodes>
          <leafValues>
            -5.9658926725387573e-001 2.7228885889053345e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 217581168 -538349634 1062631419 1039868926
            -1090707460 -2228359 -1078042693 -1147128518</internalNodes>
          <leafValues>
            -4.5812287926673889e-001 3.7063929438591003e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-2.5802578926086426e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 35 -513 -706873891 -270541825 1564475391 -120602625
            -118490145 -3162113 -1025</internalNodes>
          <leafValues>
            -8.9068460464477539e-001 -1.6470588743686676e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 -1025 872144563 -2105361 -1078076417 -1048577
            -1145061461 -87557413 -1375993973</internalNodes>
          <leafValues>
            -7.1808964014053345e-001 2.2022204473614693e-002</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 -42467849 967946223 -811601986 1030598351
            -1212430676 270856533 -1392539508 147705039</internalNodes>
          <leafValues>
            -4.9424821138381958e-001 3.0048963427543640e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -218116370 -637284625 -87373174 -521998782
            -805355450 -615023745 -814267322 -12069282</internalNodes>
          <leafValues>
            -5.5306458473205566e-001 2.9137542843818665e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 -275849241 -527897 -11052049 -69756067 -15794193
            -1141376839 -564771 -287095455</internalNodes>
          <leafValues>
            -4.6759819984436035e-001 3.6638516187667847e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -1900898096 -18985228 -44056577 -24675 -1074880639
            -283998 796335613 -1079041957</internalNodes>
          <leafValues>
            -4.2737138271331787e-001 3.9243003726005554e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -555790844 410735094 -32106513 406822863 -897632192
            -912830145 -117771560 -1204027649</internalNodes>
          <leafValues>
            -4.1896930336952209e-001 3.6744937300682068e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -1884822366 -1406613148 1135342180 -1979127580
            -68174862 246469804 1001386992 -708885872</internalNodes>
          <leafValues>
            -5.7093089818954468e-001 2.9880744218826294e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 -469053950 1439068142 2117758841 2004671078
            207931006 1265321675 970353931 1541343047</internalNodes>
          <leafValues>
            -6.0491901636123657e-001 2.4652053415775299e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-2.2425732612609863e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 58 1481987157 282547485 -14952129 421131223 -391065352
            -24212488 -100094241 -1157907473</internalNodes>
          <leafValues>
            -8.2822084426879883e-001 -2.1619293093681335e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 -134217889 -543174305 -75497474 -16851650 -6685738
            -75834693 -2097200 -262146</internalNodes>
          <leafValues>
            -5.4628932476043701e-001 2.7662658691406250e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -220728227 -604288517 -661662214 413104863
            -627323700 -251915415 -626200872 -1157958657</internalNodes>
          <leafValues>
            -4.1643124818801880e-001 4.1700571775436401e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -186664033 -44236961 -1630262774 -65163606 -103237330
            -3083265 -1003729 2053105955</internalNodes>
          <leafValues>
            -5.4847818613052368e-001 2.9710745811462402e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -256115886 -237611873 -620250696 387061799
            1437882671 274878849 -8684449 1494294023</internalNodes>
          <leafValues>
            -4.6202757954597473e-001 3.3915829658508301e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -309400577 -275864640 -1056864869 1737132756
            -272385089 1609671419 1740601343 1261376789</internalNodes>
          <leafValues>
            -4.6158722043037415e-001 3.3939516544342041e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 818197248 -196324552 286970589 -573270699
            -1174099579 -662077381 -1165157895 -1626859296</internalNodes>
          <leafValues>
            -4.6193107962608337e-001 3.2456985116004944e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 -1042550357 14675409 1367955200 -841482753
            1642443255 8774277 1941304147 1099949563</internalNodes>
          <leafValues>
            -4.9091196060180664e-001 3.3870378136634827e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 -639654997 1375720439 -2129542805 1614801090
            -626787937 -5779294 1488699183 -525406458</internalNodes>
          <leafValues>
            -4.9073097109794617e-001 3.0637946724891663e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-1.2258235216140747e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 118 302046707 -16744240 1360106207 -543735387
            1025700851 -1079408512 1796961263 -6334981</internalNodes>
          <leafValues>
            -6.1358314752578735e-001 2.3539231717586517e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -144765953 -116448726 -653851877 1934829856 722021887
            856564834 1933919231 -540838029</internalNodes>
          <leafValues>
            -5.1209545135498047e-001 3.2506987452507019e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 -170132825 -1438923874 1879300370 -1689337194
            -695606496 285911565 -1044188928 -154210028</internalNodes>
          <leafValues>
            -5.1769560575485229e-001 3.2290914654731750e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 -140776261 -355516414 822178224 -1039743806
            -1012208926 134887424 1438876097 -908591660</internalNodes>
          <leafValues>
            -5.0321841239929199e-001 3.0263835191726685e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 -2137211696 -1634281249 1464325973 498569935
            -1580152080 -2001687927 721783561 265096035</internalNodes>
          <leafValues>
            -4.6532225608825684e-001 3.4638473391532898e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 -255073589 -211824417 -972195129 -1063415417
            1937994261 1363165220 -754733105 1967602541</internalNodes>
          <leafValues>
            -4.9611270427703857e-001 3.3260712027549744e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 -548146862 -655567194 -2062466596 1164562721
            416408236 -1591631712 -83637777 975344427</internalNodes>
          <leafValues>
            -4.9862930178642273e-001 3.2003280520439148e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -731904652 2147179896 2147442687 2112830847 -65604
            -131073 -42139667 -1074907393</internalNodes>
          <leafValues>
            -3.6636069416999817e-001 4.5651626586914063e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 1885036886 571985932 -1784930633 724431327
            1940422257 -1085746880 964888398 731867951</internalNodes>
          <leafValues>
            -5.2619713544845581e-001 3.2635414600372314e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-1.3604533672332764e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 8 -287609985 -965585953 -2146397793 -492129894
            -729029645 -544619901 -645693256 -6565484</internalNodes>
          <leafValues>
            -4.5212322473526001e-001 3.8910505175590515e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 -102903523 -145031013 536899675 688195859
            -645291520 -1165359094 -905565928 171608223</internalNodes>
          <leafValues>
            -4.9594074487686157e-001 3.4109055995941162e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 -790640459 487931983 1778450522 1036604041
            -904752984 -954040118 -2134707506 304866043</internalNodes>
          <leafValues>
            -4.1148442029953003e-001 3.9666590094566345e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -303829117 1726939070 922189815 -827983123
            1567883042 1324809852 292710260 -942678754</internalNodes>
          <leafValues>
            -3.5154473781585693e-001 4.8011952638626099e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 -161295376 -159215460 -1858041315 2140644499
            -2009065472 -133804007 -2003265301 1263206851</internalNodes>
          <leafValues>
            -4.2808216810226440e-001 3.9841541647911072e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -264248081 -667846464 1342624856 1381160835
            -2104716852 1342865409 -266612310 -165954877</internalNodes>
          <leafValues>
            -4.3293288350105286e-001 4.0339657664299011e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 -1600388464 -40369901 285344639 1394344275
            -255680312 -100532214 -1031663944 -7471079</internalNodes>
          <leafValues>
            -4.1385015845298767e-001 4.5087572932243347e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 1368521651 280207469 35779199 -105983261 1208124819
            -565870452 -1144024288 -591535344</internalNodes>
          <leafValues>
            -4.2956474423408508e-001 4.2176279425621033e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 1623607527 -661513115 -1073217263 -2142994420
            -1339883309 -89816956 436308899 1426178059</internalNodes>
          <leafValues>
            -4.7764992713928223e-001 3.7551075220108032e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 15 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-4.2518746852874756e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 135 -116728032 -1154420809 -1350582273 746061691
            -1073758277 2138570623 2113797566 -138674182</internalNodes>
          <leafValues>
            -1.7125381529331207e-001 6.5421247482299805e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 -453112432 -1795354691 -1342242964 494112553
            209458404 -2114697500 1316830362 259213855</internalNodes>
          <leafValues>
            -3.9870172739028931e-001 4.5807033777236938e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 -268172036 294715533 268575185 486785157 -1065303920
            -360185856 -2147476808 134777113</internalNodes>
          <leafValues>
            -5.3581339120864868e-001 3.5815808176994324e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 -301996882 -345718921 1877946252 -940720129
            -58737369 -721944585 -92954835 -530449</internalNodes>
          <leafValues>
            -3.9938014745712280e-001 4.9603295326232910e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -853281886 -756895766 2130706352 -9519120
            -1921059862 394133373 2138453959 -538200841</internalNodes>
          <leafValues>
            -4.0230083465576172e-001 4.9537116289138794e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 -2133448688 -641138493 1078022185 294060066
            -327122776 -2130640896 -2147466247 -1910634326</internalNodes>
          <leafValues>
            -5.8290809392929077e-001 3.4102553129196167e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 587265978 -2071658479 1108361221 -578448765
            -1811905899 -2008965119 33900729 762301595</internalNodes>
          <leafValues>
            -4.5518967509269714e-001 4.7242793440818787e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 -1022189373 -2139094976 16658 -1069445120
            -1073555454 -1073577856 1096068 -978351488</internalNodes>
          <leafValues>
            -4.7530207037925720e-001 4.3885371088981628e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 -395352441 -1073541103 -1056964605 1053186 269111298
            -2012184576 1611208714 -360415095</internalNodes>
          <leafValues>
            -5.0448113679885864e-001 4.1588482260704041e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 16 -->
    <_>
      <maxWeakCount>7</maxWeakCount>
      <stageThreshold>2.7163455262780190e-002</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 49 783189748 -137429026 -257 709557994 2130460236
            -196611 -9580 585428708</internalNodes>
          <leafValues>
            -2.0454545319080353e-001 7.9608374834060669e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 1284360448 1057423155 1592696573 -852672655
            1547382714 -1642594369 125705358 797134398</internalNodes>
          <leafValues>
            -3.6474677920341492e-001 6.0925579071044922e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 1347680270 -527720448 1091567712 1073745933
            -1073180671 0 285745154 -511192438</internalNodes>
          <leafValues>
            -4.6406838297843933e-001 5.5626088380813599e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 1705780944 -145486260 -115909 -281793505 -418072663
            -1681064068 1877454127 -1912330993</internalNodes>
          <leafValues>
            -4.7043186426162720e-001 5.8430361747741699e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 -2118142016 339509033 -285260567 1417764573
            68144392 -468879483 -2033291636 231451911</internalNodes>
          <leafValues>
            -4.8700931668281555e-001 5.4639810323715210e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 -1888051818 489996135 -65539 849536890 2146716845
            -1107542088 -1275615746 -1119617586</internalNodes>
          <leafValues>
            -4.3356490135192871e-001 6.5175366401672363e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -1879021438 336830528 1073766659 1477541961 8560696
            -1207369568 8462472 1493893448</internalNodes>
          <leafValues>
            -5.4343086481094360e-001 5.2777874469757080e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 17 -->
    <_>
      <maxWeakCount>7</maxWeakCount>
      <stageThreshold>4.9174150824546814e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 57 644098 15758324 1995964260 -463011882 893285175
            83156983 2004317989 16021237</internalNodes>
          <leafValues>
            -1.7073170840740204e-001 9.0782123804092407e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 268632845 -2147450864 -2143240192 -2147401728
            8523937 -1878523840 16777416 616824984</internalNodes>
          <leafValues>
            -4.8744434118270874e-001 7.3311311006546021e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 -2110735872 803880886 989739810 1673281312 91564930
            -277454958 997709514 -581366443</internalNodes>
          <leafValues>
            -4.0291741490364075e-001 8.2450771331787109e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 941753434 -1067128905 788512753 -1074450460
            779101657 -1346552460 938805167 -2050424642</internalNodes>
          <leafValues>
            -3.6246949434280396e-001 8.7103593349456787e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 208 1645217920 130 538263552 33595552 -1475870592
            16783361 1375993867</internalNodes>
          <leafValues>
            -6.1472141742706299e-001 5.9707164764404297e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 1860423179 1034692624 -285213187 -986681712
            1576755092 -1408205463 -127714 -1246035687</internalNodes>
          <leafValues>
            -4.5621752738952637e-001 8.9482426643371582e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 33555004 -1861746688 1073807361 -754909184
            645922856 8388608 134250648 419635458</internalNodes>
          <leafValues>
            -5.2466005086898804e-001 7.1834069490432739e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 18 -->
    <_>
      <maxWeakCount>2</maxWeakCount>
      <stageThreshold>1.9084988832473755e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 16 536064 131072 -20971516 524288 576 1048577 0 40960</internalNodes>
          <leafValues>
            -8.0000001192092896e-001 9.8018401861190796e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 67108864 0 4096 1074003968 8192 536870912 4 262144</internalNodes>
          <leafValues>
            -9.6610915660858154e-001 9.2831486463546753e-001</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rect>
        0 0 1 1</rect></_>
    <_>
      <rect>
        0 0 3 2</rect></_>
    <_>
      <rect>
        0 1 13 6</rect></_>
    <_>
      <rect>
        0 2 3 14</rect></_>
    <_>
      <rect>
        0 2 4 2</rect></_>
    <_>
      <rect>
        0 6 2 3</rect></_>
    <_>
      <rect>
        0 6 3 2</rect></_>
    <_>
      <rect>
        0 16 1 3</rect></_>
    <_>
      <rect>
        0 20 3 3</rect></_>
    <_>
      <rect>
        0 22 2 3</rect></_>
    <_>
      <rect>
        0 28 4 4</rect></_>
    <_>
      <rect>
        0 35 2 3</rect></_>
    <_>
      <rect>
        1 0 14 7</rect></_>
    <_>
      <rect>
        1 5 3 2</rect></_>
    <_>
      <rect>
        1 6 2 1</rect></_>
    <_>
      <rect>
        1 14 10 9</rect></_>
    <_>
      <rect>
        1 21 4 4</rect></_>
    <_>
      <rect>
        1 23 4 2</rect></_>
    <_>
      <rect>
        2 0 13 7</rect></_>
    <_>
      <rect>
        2 0 14 7</rect></_>
    <_>
      <rect>
        2 33 5 4</rect></_>
    <_>
      <rect>
        2 36 4 3</rect></_>
    <_>
      <rect>
        2 39 3 2</rect></_>
    <_>
      <rect>
        3 1 13 11</rect></_>
    <_>
      <rect>
        3 2 3 2</rect></_>
    <_>
      <rect>
        4 0 7 8</rect></_>
    <_>
      <rect>
        4 0 13 7</rect></_>
    <_>
      <rect>
        5 0 12 6</rect></_>
    <_>
      <rect>
        5 0 13 7</rect></_>
    <_>
      <rect>
        5 1 10 13</rect></_>
    <_>
      <rect>
        5 1 12 7</rect></_>
    <_>
      <rect>
        5 2 7 13</rect></_>
    <_>
      <rect>
        5 4 2 1</rect></_>
    <_>
      <rect>
        5 8 7 4</rect></_>
    <_>
      <rect>
        5 39 3 2</rect></_>
    <_>
      <rect>
        6 3 5 2</rect></_>
    <_>
      <rect>
        6 3 6 2</rect></_>
    <_>
      <rect>
        6 5 4 12</rect></_>
    <_>
      <rect>
        6 9 6 3</rect></_>
    <_>
      <rect>
        7 3 5 2</rect></_>
    <_>
      <rect>
        7 3 6 13</rect></_>
    <_>
      <rect>
        7 5 6 4</rect></_>
    <_>
      <rect>
        7 7 6 10</rect></_>
    <_>
      <rect>
        7 8 6 4</rect></_>
    <_>
      <rect>
        7 32 5 4</rect></_>
    <_>
      <rect>
        7 33 5 4</rect></_>
    <_>
      <rect>
        8 0 1 1</rect></_>
    <_>
      <rect>
        8 0 2 1</rect></_>
    <_>
      <rect>
        8 2 10 7</rect></_>
    <_>
      <rect>
        9 0 6 2</rect></_>
    <_>
      <rect>
        9 2 9 3</rect></_>
    <_>
      <rect>
        9 4 1 1</rect></_>
    <_>
      <rect>
        9 6 2 1</rect></_>
    <_>
      <rect>
        9 28 6 4</rect></_>
    <_>
      <rect>
        10 0 9 3</rect></_>
    <_>
      <rect>
        10 3 1 1</rect></_>
    <_>
      <rect>
        10 10 11 11</rect></_>
    <_>
      <rect>
        10 15 4 3</rect></_>
    <_>
      <rect>
        11 4 2 1</rect></_>
    <_>
      <rect>
        11 27 4 3</rect></_>
    <_>
      <rect>
        11 36 8 2</rect></_>
    <_>
      <rect>
        12 0 2 2</rect></_>
    <_>
      <rect>
        12 23 4 3</rect></_>
    <_>
      <rect>
        12 25 4 3</rect></_>
    <_>
      <rect>
        12 29 5 3</rect></_>
    <_>
      <rect>
        12 33 3 4</rect></_>
    <_>
      <rect>
        13 0 2 2</rect></_>
    <_>
      <rect>
        13 36 8 3</rect></_>
    <_>
      <rect>
        14 0 2 2</rect></_>
    <_>
      <rect>
        15 15 2 2</rect></_>
    <_>
      <rect>
        16 13 3 4</rect></_>
    <_>
      <rect>
        17 0 1 3</rect></_>
    <_>
      <rect>
        17 1 3 3</rect></_>
    <_>
      <rect>
        17 31 5 3</rect></_>
    <_>
      <rect>
        17 35 3 1</rect></_>
    <_>
      <rect>
        18 13 2 3</rect></_>
    <_>
      <rect>
        18 39 2 1</rect></_>
    <_>
      <rect>
        19 0 7 15</rect></_>
    <_>
      <rect>
        19 2 7 2</rect></_>
    <_>
      <rect>
        19 3 7 13</rect></_>
    <_>
      <rect>
        19 14 2 2</rect></_>
    <_>
      <rect>
        19 24 7 4</rect></_>
    <_>
      <rect>
        20 1 6 13</rect></_>
    <_>
      <rect>
        20 8 7 3</rect></_>
    <_>
      <rect>
        20 9 7 3</rect></_>
    <_>
      <rect>
        20 13 1 1</rect></_>
    <_>
      <rect>
        20 14 2 3</rect></_>
    <_>
      <rect>
        20 30 3 2</rect></_>
    <_>
      <rect>
        21 0 3 4</rect></_>
    <_>
      <rect>
        21 0 6 8</rect></_>
    <_>
      <rect>
        21 3 6 2</rect></_>
    <_>
      <rect>
        21 6 6 4</rect></_>
    <_>
      <rect>
        21 37 2 1</rect></_>
    <_>
      <rect>
        22 3 6 2</rect></_>
    <_>
      <rect>
        22 13 1 2</rect></_>
    <_>
      <rect>
        22 22 4 3</rect></_>
    <_>
      <rect>
        23 0 2 3</rect></_>
    <_>
      <rect>
        23 3 6 2</rect></_>
    <_>
      <rect>
        23 9 5 4</rect></_>
    <_>
      <rect>
        23 11 1 1</rect></_>
    <_>
      <rect>
        23 15 1 1</rect></_>
    <_>
      <rect>
        23 16 3 2</rect></_>
    <_>
      <rect>
        23 35 2 1</rect></_>
    <_>
      <rect>
        23 36 1 1</rect></_>
    <_>
      <rect>
        23 39 6 2</rect></_>
    <_>
      <rect>
        24 0 2 3</rect></_>
    <_>
      <rect>
        24 8 6 11</rect></_>
    <_>
      <rect>
        24 28 2 2</rect></_>
    <_>
      <rect>
        24 33 4 4</rect></_>
    <_>
      <rect>
        25 16 4 3</rect></_>
    <_>
      <rect>
        25 31 5 3</rect></_>
    <_>
      <rect>
        26 0 1 2</rect></_>
    <_>
      <rect>
        26 0 2 2</rect></_>
    <_>
      <rect>
        26 0 3 2</rect></_>
    <_>
      <rect>
        26 24 4 4</rect></_>
    <_>
      <rect>
        27 30 4 5</rect></_>
    <_>
      <rect>
        27 36 5 3</rect></_>
    <_>
      <rect>
        28 0 2 2</rect></_>
    <_>
      <rect>
        28 4 2 1</rect></_>
    <_>
      <rect>
        28 21 2 5</rect></_>
    <_>
      <rect>
        29 8 2 1</rect></_>
    <_>
      <rect>
        33 0 2 1</rect></_>
    <_>
      <rect>
        33 0 4 2</rect></_>
    <_>
      <rect>
        33 0 4 6</rect></_>
    <_>
      <rect>
        33 3 1 1</rect></_>
    <_>
      <rect>
        33 6 4 12</rect></_>
    <_>
      <rect>
        33 21 4 2</rect></_>
    <_>
      <rect>
        33 36 4 3</rect></_>
    <_>
      <rect>
        35 1 2 2</rect></_>
    <_>
      <rect>
        36 5 1 1</rect></_>
    <_>
      <rect>
        36 29 3 4</rect></_>
    <_>
      <rect>
        36 39 2 2</rect></_>
    <_>
      <rect>
        37 5 2 2</rect></_>
    <_>
      <rect>
        38 6 2 1</rect></_>
    <_>
      <rect>
        38 6 2 2</rect></_>
    <_>
      <rect>
        39 1 2 12</rect></_>
    <_>
      <rect>
        39 24 1 2</rect></_>
    <_>
      <rect>
        39 36 2 2</rect></_>
    <_>
      <rect>
        40 39 1 2</rect></_>
    <_>
      <rect>
        42 4 1 1</rect></_>
    <_>
      <rect>
        42 20 1 2</rect></_>
    <_>
      <rect>
        42 29 1 2</rect></_></features></cascade>
</opencv_storage>
