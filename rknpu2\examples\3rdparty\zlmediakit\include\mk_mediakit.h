﻿/*
 * Copyright (c) 2016 The ZLMediaKit project authors. All Rights Reserved.
 *
 * This file is part of ZLMediaKit(https://github.com/xia-chu/ZLMediaKit).
 *
 * Use of this source code is governed by MIT license that can be found in the
 * LICENSE file in the root of the source tree. All contributing project authors
 * may be found in the AUTHORS file in the root of the source tree.
 */

#ifndef MK_API_H_
#define MK_API_H_

#include "mk_common.h"
#include "mk_httpclient.h"
#include "mk_media.h"
#include "mk_proxyplayer.h"
#include "mk_recorder.h"
#include "mk_player.h"
#include "mk_pusher.h"
#include "mk_events.h"
#include "mk_tcp.h"
#include "mk_util.h"
#include "mk_thread.h"
#include "mk_rtp_server.h"
#include "mk_h264_splitter.h"
#include "mk_frame.h"
#include "mk_track.h"
#include "mk_transcode.h"

#endif /* MK_API_H_ */
